import json
import os
import boto3
from botocore.exceptions import ClientError
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


def get_s3_client():
    if os.environ.get('IS_OFFLINE'):
        return boto3.client(
            's3',
            region_name=os.environ.get('AWS_REGION', 'us-east-1')
        )
    else:
        return boto3.client('s3')


s3 = get_s3_client()
S3_BUCKET = os.environ.get('S3_BUCKET')


def initiate_multipart_upload(file_name, user_id, content_type='application/octet-stream'):
    try:
        timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
        file_extension = os.path.splitext(file_name)[1]
        s3_key = f"users/{user_id}/uploads/{timestamp}_multipart_{file_extension}"

        response = s3.create_multipart_upload(
            Bucket=S3_BUCKET,
            Key=s3_key,
            ContentType=content_type,
            Metadata={
                'user-id': user_id,
                'original-name': file_name,
                'upload-type': 'multipart'
            }
        )

        return {
            'uploadId': response['UploadId'],
            's3Key': s3_key,
            'bucket': S3_BUCKET
        }

    except ClientError as e:
        logger.error(f"Error initiating multipart upload: {e}")
        raise e


def generate_part_upload_urls(s3_key, upload_id, parts, expiration=900):
    try:
        presigned_urls = []

        for part_number in range(1, parts + 1):
            # For multipart upload parts, we need to use generate_presigned_post or
            # include Content-Type in the signature calculation differently
            url = s3.generate_presigned_url(
                'upload_part',
                Params={
                    'Bucket': S3_BUCKET,
                    'Key': s3_key,
                    'UploadId': upload_id,
                    'PartNumber': part_number
                },
                ExpiresIn=expiration,
                HttpMethod='PUT'
            )

            presigned_urls.append({
                'partNumber': part_number,
                'url': url
            })

        logger.info(
            f"Generated {parts} presigned URLs for multipart upload {upload_id}")

        return presigned_urls

    except ClientError as e:
        logger.error(f"Error generating part upload URLs: {e}")
        raise e


def complete_multipart_upload(s3_key, upload_id, parts):
    try:
        multipart_upload = {
            'Parts': [
                {
                    'PartNumber': part['partNumber'],
                    'ETag': part['eTag']
                }
                for part in sorted(parts, key=lambda x: x['partNumber'])
            ]
        }

        response = s3.complete_multipart_upload(
            Bucket=S3_BUCKET,
            Key=s3_key,
            UploadId=upload_id,
            MultipartUpload=multipart_upload
        )

        return {
            'location': response.get('Location'),
            'bucket': response.get('Bucket'),
            'key': response.get('Key'),
            'eTag': response.get('ETag')
        }

    except ClientError as e:
        logger.error(f"Error completing multipart upload: {e}")
        raise e


def abort_multipart_upload(s3_key, upload_id):
    try:
        s3.abort_multipart_upload(
            Bucket=S3_BUCKET,
            Key=s3_key,
            UploadId=upload_id
        )

        return True

    except ClientError as e:
        logger.error(f"Error aborting multipart upload: {e}")
        if e.response['Error']['Code'] == 'NoSuchUpload':
            return False
        raise e


def list_multipart_uploads(prefix=None):
    try:
        params = {'Bucket': S3_BUCKET}
        if prefix:
            params['Prefix'] = prefix

        response = s3.list_multipart_uploads(**params)

        uploads = []
        for upload in response.get('Uploads', []):
            uploads.append({
                'key': upload['Key'],
                'uploadId': upload['UploadId'],
                'initiated': upload['Initiated'].isoformat()
            })

        return uploads

    except ClientError as e:
        logger.error(f"Error listing multipart uploads: {e}")
        raise e


def cleanup_old_multipart_uploads(hours=24):
    try:
        cutoff_time = datetime.utcnow().timestamp() - (hours * 3600)
        uploads = list_multipart_uploads()

        cleaned = 0
        for upload in uploads:
            initiated_time = datetime.fromisoformat(
                upload['initiated'].replace('Z', '+00:00')).timestamp()
            if initiated_time < cutoff_time:
                if abort_multipart_upload(upload['key'], upload['uploadId']):
                    cleaned += 1
                    logger.info(
                        f"Cleaned up old multipart upload: {upload['key']}")

        return cleaned

    except Exception as e:
        logger.error(f"Error cleaning up multipart uploads: {e}")
        return 0
